"use client";

import React, { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { X, Plus, Edit2, Check, XCircle } from "lucide-react";

interface SimpleTextArrayProps {
  value: string[];
  onChange: (value: string[]) => void;
  placeholder?: string;
  disabled?: boolean;
  maxItems?: number;
  helpText?: string;
}

export const SimpleTextArray: React.FC<SimpleTextArrayProps> = ({
  value = [],
  onChange,
  placeholder = "Enter text",
  disabled = false,
  maxItems = 50,
  helpText,
}) => {
  const [inputValue, setInputValue] = useState("");
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [editValue, setEditValue] = useState("");

  const items: string[] = Array.isArray(value) ? value : [];

  const addItem = () => {
    if (inputValue.trim() && !items.includes(inputValue.trim())) {
      if (items.length < maxItems) {
        onChange([...items, inputValue.trim()]);
        setInputValue("");
      }
    }
  };

  const removeItem = (index: number) => {
    const newItems = items.filter((_, i) => i !== index);
    onChange(newItems);
  };

  const startEdit = (index: number) => {
    setEditingIndex(index);
    setEditValue(items[index]);
  };

  const saveEdit = () => {
    if (editValue.trim() && editingIndex !== null) {
      const newItems = [...items];
      newItems[editingIndex] = editValue.trim();
      onChange(newItems);
      setEditingIndex(null);
      setEditValue("");
    }
  };

  const cancelEdit = () => {
    setEditingIndex(null);
    setEditValue("");
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      addItem();
    }
  };

  const handleEditKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && e.ctrlKey) {
      e.preventDefault();
      saveEdit();
    } else if (e.key === "Escape") {
      e.preventDefault();
      cancelEdit();
    }
  };

  return (
    <div className="border-2 border-dashed border-border rounded-lg p-4 bg-background/50">
      {/* Input row */}
      <div className="flex gap-2 mb-3">
        <Input
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          disabled={disabled}
        />
        <Button
          type="button"
          onClick={addItem}
          disabled={
            disabled || !inputValue.trim() || items.length >= maxItems
          }
          size="sm"
          variant="outline"
        >
          <Plus className="h-4 w-4" />
        </Button>
      </div>

      {/* Items display */}
      {items.length > 0 && (
        <div className="space-y-2">
          {items.map((item, index) => (
            <div
              key={index}
              className="border border-border rounded-md p-3 bg-card shadow-sm"
            >
              {editingIndex === index ? (
                <div className="space-y-2">
                  <Textarea
                    value={editValue}
                    onChange={(e) => setEditValue(e.target.value)}
                    onKeyDown={handleEditKeyPress}
                    className="min-h-[60px] resize-none"
                    placeholder="Edit text..."
                    autoFocus
                  />
                  <div className="flex gap-2">
                    <Button
                      type="button"
                      onClick={saveEdit}
                      size="sm"
                      variant="default"
                      className="h-7"
                    >
                      <Check className="h-3 w-3 mr-1" />
                      Save
                    </Button>
                    <Button
                      type="button"
                      onClick={cancelEdit}
                      size="sm"
                      variant="outline"
                      className="h-7"
                    >
                      <XCircle className="h-3 w-3 mr-1" />
                      Cancel
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="flex items-start justify-between gap-3">
                  <div className="flex-1 min-w-0">
                    <p className="text-sm text-foreground break-words whitespace-pre-wrap">
                      {item}
                    </p>
                  </div>
                  <div className="flex gap-1 flex-shrink-0">
                    <Button
                      type="button"
                      onClick={() => startEdit(index)}
                      size="sm"
                      variant="ghost"
                      className="h-7 w-7 p-0"
                      disabled={disabled}
                    >
                      <Edit2 className="h-3 w-3" />
                    </Button>
                    <Button
                      type="button"
                      onClick={() => removeItem(index)}
                      size="sm"
                      variant="ghost"
                      className="h-7 w-7 p-0 text-destructive hover:text-destructive"
                      disabled={disabled}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Help text and item count */}
      <div className="mt-3 flex justify-between items-center text-xs text-muted-foreground">
        <span>
          {items.length} / {maxItems} items
        </span>
        {helpText && <span>{helpText}</span>}
      </div>

      {/* Empty state */}
      {items.length === 0 && (
        <div className="text-center py-4 text-sm text-muted-foreground">
          No items added yet. Type above and press Enter or click + to add.
        </div>
      )}
    </div>
  );
};
