{"table": {"name": "product_variants", "primary_key": "id", "display_field": "variant_name", "hasura_table_name": "health_product_variants"}, "fields": [{"name": "id", "type": "text", "required": true, "ui_config": {"label": "Variant ID", "placeholder": "Variant ID"}, "max_length": 50, "primary_key": true, "graphql_type": "String"}, {"name": "variant_name", "type": "text", "required": true, "ui_config": {"label": "Variant Name", "placeholder": "Enter variant name"}, "max_length": 255, "graphql_type": "String", "is_option_title": true}, {"name": "variant_slug", "type": "text", "unique": true, "required": true, "ui_config": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "variant-url-slug"}, "max_length": 255, "graphql_type": "String"}, {"name": "product_id", "type": "text", "required": true, "ui_config": {"label": "Product", "widget": "relationship_select", "display_field": "name"}, "foreign_key": {"table": "products", "column": "id"}, "graphql_type": "String!"}]}